## 新模型订单号设计
- 单号长度: 19位
  - 首位数字表示: 1-会员 3-会员(迁移订单) 2-非会员 4-非会员(迁移订单)
  - 2-15位: 雪花算法
  - 16-19: 分表位置

## 线下订单分表核心架构组件

**路由层设计**：
- 系统首先根据用户类型进行初步路由分发
- 非会员用户和会员用户采用不同的数据存储策略

**非会员用户处理流程**：
- 采用基于时间维度的分表策略
- 按照"取年后2位+月份"的规则生成表标识（如2025年1月对应"2501"）
- 最终路由到对应的分表位置

**会员用户分表算法**：
- 基于用户ID的哈希分表机制
- 哈希函数计算：`userId % 1024`，支持1024个分表
- 根据哈希值的范围分布到不同的虚拟表（0-4, 4-8, ..., 1020-1024）
- 每个虚拟表对应具体的物理表（物理表_0, 物理表_1, ..., 物理表_255）

## 技术特性

**扩展性设计**：
- 哈希取模算法确保数据均匀分布
- 支持256个物理分表，理论上可承载大规模用户数据

**性能优化**：
- 通过用户类型预分流，减少路由计算开销
- 哈希算法O(1)时间复杂度，保证高并发场景下的路由效率

**数据治理**：
- 非会员数据按时间分区，便于历史数据归档和清理
- 会员数据按哈希分布，避免热点数据集中

这种混合分表策略兼顾了不同用户类型的数据特征，既保证了查询性能，又提供了良好的水平扩展能力。


## 示例
- 1555064196503130255 解释: 会员订单,在255分表
- 2131391662597162507 解释: 非会员订单,在2507分表
- 3433998062192730012 解释: 会员,迁移订单,在12分表
- 4381705856542812410 解释: 非会员订单,在2410分表