## 订单服务

### 结构说明
- 服务名
  - 说明: 服务简介
  - 状态: 服务状态。 现有如下状态: 正常、即将废弃、已废弃
  - tags: 标签 。 现有如下标签: 原子服务
  - 功能: 功能点
    - 功能点: 功能描述

### 订单服务
- assignment-engine
    - 说明: 异步任务工具包
    - 状态: 正常
- customer-service-center
    - 说明: 客服中台
    - 状态: 正常
- evaluation-center
    - 说明: 评价中台
    - 状态: 正常
- hydee-business-order
    - 说明: 订单中台-O2O
    - 状态: 即将废弃
- hydee-business-order-ext
    - 说明: 订单数据-归档服务;暂时未启用
    - 状态: 已废弃
- hydee-business-order-lua
    - 说明: 
    - 状态: 已废弃
- hydee-business-order-web
    - 说明: 订单中台 B2C
    - 状态: 即将废弃
- hydee-middle-order
    - 说明: 订单-微商城-小前台
    - 状态: 正常
- hydee-middle-sdp
    - 说明:
    - 状态: 正常
- hydee-oms-tool
    - 说明: 研发脚手架 1.调用三方物流平台.  中通/圆通/邮政/极兔 2.JAVA调用.net的sdk
    - 状态: 正常
- hydee-third-inside
    - 说明: 物流中台-调用三方物流平台接口
    - 状态: 正常
- hydeeprintserver
    - 说明: 打印播报中台, .NET打印助手
    - 状态: 正常
- logistics-center
    - 说明: 物流中台
    - 状态: 正常
- message-icsim-server
    - 说明:
    - 状态: 废弃
- middle-datasync-message
    - 说明: 订单同步服务 订单-微商城-小前台订单同步至.NET
    - 状态: 正常
- oms-client
    - 说明: 王红元提交.门店切换期间问题解决工具
    - 状态: 废弃
- order-account-atom-service
    - 说明: 重构后-订单中台-下账原子服务
    - 状态: 正常
    - tags: 原子服务
- order-after-sale-atom-service
    - 说明: 重构后-订单中台-售后原子服务
    - 状态: 正常
    - tags: 原子服务
- order-assistant
    - 说明: 订单AI助手
    - 状态: 正常
- order-atom-service
    - 说明: 订单中台-原子服务
    - 状态: 正常
    - tags: 原子服务
- order-batch-processing
    - 说明: 订单中台-订单批处理任务
    - 状态: 正常
- order-billing-service
    - 说明: 订单下账服务
    - 状态: 正常
- order-config-center
    - 说明: 订单中台-配置中心
    - 状态: 正常
- order-consistent-center
    - 说明: 订单中台-订单监控服务。
    - 状态: 正常
    - 功能:
      - 海典一致性对账
- order-delivery-atom-service
    - 说明: 订单中台-发货原子服务
    - 状态: 正常
    - tags: 原子服务
- order-framework
    - 说明: 研发脚手架-订单域通用组件
    - 状态: 正常
    - 功能:
      - order-types: 维护Domain Private对象
      - order-common: 维护订单通用能力
      - order-permission: 维护订单权限
- order-reconciliation
    - 说明: 订单中台-公域对账
    - 状态: 正常
- order-service
    - 说明: 订单中台-核心服务(P0级服务,影响到门店生产作业的相关服务. 需要同步完成调用的)
    - 状态: 正常
    - 功能:
      - 订单拣货
      - 订单下账
- order-split-server
    - 说明: 订单中台-订单拆合单服务
    - 状态: 正常
- order-sync
    - 说明: 重构后-订单中台-异步服务,主要处理非核心业务
    - 状态: 正常
    - 功能:
      - 海典线下订单同步
      - 科传线下订单同步
      - 异常MQ消息定时补偿逻辑
- order-transfer-system
    - 说明: 
    - 状态: 正常
- purchase-order-center
    - 说明: 采购中台
    - 状态: 正常
- start-up-optimization
    - 说明: 启动优化。术语研发自用的工具
    - 状态: 正常
- vscode_demo
    - 说明: 基于vscode的demo
    - 状态: 废弃
- yxt-mock-server
    - 说明: 数据Mock服务
    - 状态: 正常
- yxt-payment
    - 说明: 支付中台
    - 状态: 正常
- yxt-trade-center
    - 说明: 交易中台
    - 状态: 正常
- risk-control-service
  - 说明: 风控服务
  - 状态: 正常


## 网关服务

### 网关各个环境域名
- 生产内网：api.yxt.local
- 生产：api.hxyxt.com
- 预发：pre-api.hxyxt.com
- 测试：test-api.hxyxt.com
- 开发：dev-api.hxyxt.com

### 网关服务
- businesses-gateway
  - 说明: 一心助手、心云后台等面向员工的B端业务，对前端提供接口。（企业数字化网关）
  - 状态: 正常
  - url: hera
- hydee-api-gateway
  - 说明: 默认对外网关，如其它外部对接，包括美团、饿了么、ERP等
  - 状态: 正常
  - url: zeus
- api-gateway-athena
  - 说明: 海典POS相关对接
  - 状态: 正常
  - url: athena
- api-gateway-hades
  - 说明: 科传POS相关对接
  - 状态: 正常
  - url: hades
- bigdata-gateway
  - 说明: 大数据埋点等
  - 状态: 正常
  - url: ares
- api-gateway-assist
  - 说明: 一心助手移动APP，对前端提供接口
  - 状态: 正常
  - url: poseidon

### 不通过网关直接调用的域名
- 域名: open-api.hxyxt.com/微服务名/接口path
- 功能: 直接对外接口（美团、饿了么等）


## 一心堂研发脚手架
https://yxtgit.hxyxt.com/yxt-global/yxt-xframe

## 一心堂静态工具能力
https://yxtgit.hxyxt.com/yxt-global/yxt-common

- yxt-common-alarm
- yxt-common-auth
- yxt-common-ddd
- yxt-common-interceptor
- yxt-common-lang
- yxt-common-logic: 通用逻辑。交易生产组负责
- yxt-common-sign: 网关统一验签逻辑。交易生产组负责
- yxt-common-validation
- yxt-common-wechatrobot
- yxt-common-xml
