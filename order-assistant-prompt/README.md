# 提示词组装框架

一个高拓展性、高灵活性的提示词组装框架，专为Spring项目设计，支持复杂的提示词构建场景。

## 特性

- **高拓展性**: 支持插件化扩展，可以轻松添加新的提示词组件和处理器
- **高灵活性**: 支持动态配置，可以根据不同场景组装不同的提示词结构
- **链式构建**: 采用建造者模式，让提示词构建过程清晰直观
- **模板化支持**: 支持Velocity和简单模板引擎，可以使用占位符和变量替换
- **Spring集成**: 完美集成Spring生态系统，支持自动配置和依赖注入
- **线程安全**: 所有核心组件都是线程安全的

## 快速开始

### 基础使用

```java
PromptResult result = PromptBuilder.create()
    .withSystem("你是一个专业的AI助手")
    .withUser("请帮我分析这段代码")
    .withConstraint("回答要简洁明了")
    .withOutputFormat("JSON格式")
    .build();

System.out.println(result.getContent());
```

### 代码审查场景

```java
PromptResult result = PromptBuilder.create()
    .withSystem("你是一个资深的Java开发工程师")
    .withInstruction("请审查以下代码，关注代码质量、性能和安全性")
    .withContext("项目类型", "Spring Boot应用")
    .withVariable("code", codeContent)
    .withExample("输入示例", "输出示例")
    .withConstraint("输出格式为JSON，包含问题列表和建议")
    .withOutputFormat(OutputFormatComponent.json())
    .build();
```

### 模板使用

```java
PromptResult result = PromptBuilder.create()
    .withTemplate("code-review")
    .withVariable("language", "Java")
    .withVariable("code", codeContent)
    .withVariable("projectType", "Spring Boot应用")
    .build();
```

## 核心组件

### 提示词组件类型

- **SystemPromptComponent**: 系统角色设定
- **UserPromptComponent**: 用户输入
- **AssistantPromptComponent**: 助手回复（用于少样本学习）
- **ContextComponent**: 上下文信息
- **InstructionComponent**: 具体指令
- **ExampleComponent**: 示例数据
- **ConstraintComponent**: 约束条件
- **OutputFormatComponent**: 输出格式定义

### 模板引擎

- **VelocityPromptTemplate**: 基于Velocity的模板引擎，支持复杂的模板逻辑
- **SimplePromptTemplate**: 简单模板引擎，支持 `${variableName}` 语法

### 处理器

- **ValidationProcessor**: 验证处理器，用于验证组件有效性
- **TemplateProcessor**: 模板处理器，用于处理组件中的模板变量

## 配置

### application.properties

```properties
# 启用缓存
prompt.enable-cache=true

# 模板路径
prompt.template-path=templates/

# 默认编码
prompt.default-encoding=UTF-8

# 模板引擎类型 (VELOCITY, SIMPLE)
prompt.template-engine=VELOCITY

# 严格验证模式
prompt.strict-validation=false

# 组件分隔符
prompt.component-separator=\n\n

# 最大内容长度
prompt.max-content-length=10000
```

### Spring配置

框架提供了自动配置，只需要在Spring Boot应用中添加依赖即可自动启用。

```java
@Configuration
@EnableConfigurationProperties(PromptProperties.class)
public class PromptConfiguration {
    // 自动配置
}
```

## 高级用法

### 自定义组件

```java
public class CustomPromptComponent implements PromptComponent {
    @Override
    public PromptComponentType getType() {
        return PromptComponentType.CUSTOM;
    }
    
    @Override
    public String getContent(PromptContext context) {
        // 自定义内容生成逻辑
        return "自定义组件内容";
    }
}

// 使用自定义组件
PromptBuilder.create()
    .withComponent(new CustomPromptComponent())
    .build();
```

### 自定义处理器

```java
public class CustomProcessor implements PromptProcessor {
    @Override
    public List<PromptComponent> process(List<PromptComponent> components, 
                                       PromptContext context) {
        // 自定义处理逻辑
        return components;
    }
}

// 使用自定义处理器
PromptBuilder.create()
    .withProcessor(new CustomProcessor())
    .build();
```

### 条件组装

```java
PromptBuilder builder = PromptBuilder.create()
    .withSystem("你是一个AI助手");

if (needExamples) {
    builder.withExample("输入示例", "输出示例");
}

if (hasConstraints) {
    builder.withConstraint("不超过500字");
}

PromptResult result = builder.build();
```

## 测试

运行测试类来验证框架功能：

```java
// 运行基础测试
PromptFrameworkTest.main(new String[]{});

// 运行示例
PromptFrameworkExample.main(new String[]{});
```

## 最佳实践

1. **组件优先级**: 合理设置组件优先级，确保提示词结构符合预期
2. **变量命名**: 使用有意义的变量名，遵循命名规范
3. **模板复用**: 将常用的提示词结构抽取为模板，提高复用性
4. **错误处理**: 始终检查构建结果的成功状态和错误信息
5. **性能优化**: 对于频繁使用的模板，考虑启用缓存

## 许可证

本项目采用 MIT 许可证。
