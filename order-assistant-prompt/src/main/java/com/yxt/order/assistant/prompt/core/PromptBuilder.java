package com.yxt.order.assistant.prompt.core;

import java.util.Map;

/**
 * 提示词构建器接口
 * 提供链式调用的API来构建复杂的提示词
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public interface PromptBuilder {
    
    /**
     * 创建新的构建器实例
     * 
     * @return 构建器实例
     */
    static PromptBuilder create() {
        return new com.yxt.order.assistant.prompt.builder.DefaultPromptBuilder();
    }
    
    /**
     * 设置系统提示词
     * 
     * @param systemPrompt 系统提示词内容
     * @return 当前构建器实例，支持链式调用
     */
    PromptBuilder withSystem(String systemPrompt);
    
    /**
     * 设置用户输入
     * 
     * @param userInput 用户输入内容
     * @return 当前构建器实例，支持链式调用
     */
    PromptBuilder withUser(String userInput);
    
    /**
     * 设置助手回复
     * 
     * @param assistantReply 助手回复内容
     * @return 当前构建器实例，支持链式调用
     */
    PromptBuilder withAssistant(String assistantReply);
    
    /**
     * 添加上下文信息
     * 
     * @param context 上下文内容
     * @return 当前构建器实例，支持链式调用
     */
    PromptBuilder withContext(String context);
    
    /**
     * 添加上下文信息（键值对形式）
     * 
     * @param key 上下文键
     * @param value 上下文值
     * @return 当前构建器实例，支持链式调用
     */
    PromptBuilder withContext(String key, Object value);
    
    /**
     * 添加指令
     * 
     * @param instruction 指令内容
     * @return 当前构建器实例，支持链式调用
     */
    PromptBuilder withInstruction(String instruction);
    
    /**
     * 添加示例
     * 
     * @param input 示例输入
     * @param output 示例输出
     * @return 当前构建器实例，支持链式调用
     */
    PromptBuilder withExample(String input, String output);
    
    /**
     * 添加约束条件
     * 
     * @param constraint 约束条件内容
     * @return 当前构建器实例，支持链式调用
     */
    PromptBuilder withConstraint(String constraint);
    
    /**
     * 设置输出格式
     * 
     * @param outputFormat 输出格式描述
     * @return 当前构建器实例，支持链式调用
     */
    PromptBuilder withOutputFormat(String outputFormat);
    
    /**
     * 添加自定义组件
     * 
     * @param component 自定义组件
     * @return 当前构建器实例，支持链式调用
     */
    PromptBuilder withComponent(PromptComponent component);
    
    /**
     * 使用模板
     * 
     * @param templateName 模板名称
     * @return 当前构建器实例，支持链式调用
     */
    PromptBuilder withTemplate(String templateName);
    
    /**
     * 设置变量
     * 
     * @param key 变量名
     * @param value 变量值
     * @return 当前构建器实例，支持链式调用
     */
    PromptBuilder withVariable(String key, Object value);
    
    /**
     * 批量设置变量
     * 
     * @param variables 变量映射
     * @return 当前构建器实例，支持链式调用
     */
    PromptBuilder withVariables(Map<String, Object> variables);
    
    /**
     * 添加处理器
     * 
     * @param processor 处理器
     * @return 当前构建器实例，支持链式调用
     */
    PromptBuilder withProcessor(PromptProcessor processor);
    
    /**
     * 设置上下文
     * 
     * @param context 上下文实例
     * @return 当前构建器实例，支持链式调用
     */
    PromptBuilder withContext(PromptContext context);
    
    /**
     * 构建提示词
     * 
     * @return 构建结果
     */
    PromptResult build();
}
