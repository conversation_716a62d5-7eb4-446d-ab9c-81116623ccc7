package com.yxt.order.assistant.prompt.example;

import com.yxt.order.assistant.prompt.component.OutputFormatComponent;
import com.yxt.order.assistant.prompt.core.PromptBuilder;
import com.yxt.order.assistant.prompt.core.PromptResult;
import com.yxt.order.assistant.prompt.processor.TemplateProcessor;
import com.yxt.order.assistant.prompt.processor.ValidationProcessor;
import com.yxt.order.assistant.prompt.template.VelocityPromptTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 提示词框架使用示例
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class PromptFrameworkExample {
    
    public static void main(String[] args) {
        System.out.println("=== 提示词框架使用示例 ===\n");
        
        // 1. 基础使用示例
        basicUsageExample();
        
        // 2. 代码审查示例
        codeReviewExample();
        
        // 3. API文档生成示例
        apiDocumentationExample();
        
        // 4. 模板使用示例
        templateUsageExample();
        
        // 5. 自定义组件示例
        customComponentExample();
        
        // 6. 处理器链示例
        processorChainExample();
    }
    
    /**
     * 基础使用示例
     */
    public static void basicUsageExample() {
        System.out.println("1. 基础使用示例");
        System.out.println("================");
        
        PromptResult result = PromptBuilder.create()
                .withSystem("你是一个专业的AI助手，擅长回答技术问题")
                .withUser("请解释什么是Spring Boot")
                .withConstraint("回答要简洁明了，不超过200字")
                .withOutputFormat("请以Markdown格式输出")
                .build();
        
        printResult(result);
    }
    
    /**
     * 代码审查示例
     */
    public static void codeReviewExample() {
        System.out.println("2. 代码审查示例");
        System.out.println("================");
        
        String javaCode = "public class UserService {\n" +
                "    private UserRepository userRepository;\n" +
                "    \n" +
                "    public User findById(Long id) {\n" +
                "        return userRepository.findById(id).orElse(null);\n" +
                "    }\n" +
                "}";
        
        PromptResult result = PromptBuilder.create()
                .withSystem("你是一个资深的Java开发工程师")
                .withInstruction("请审查以下代码，关注代码质量、性能和安全性")
                .withContext("项目类型", "Spring Boot应用")
                .withContext("编程语言", "Java")
                .withVariable("code", javaCode)
                .withExample(
                    "public class Test { private String name; }",
                    "建议：1. 缺少getter/setter方法 2. 建议添加构造函数 3. 考虑添加字段验证"
                )
                .withConstraint("输出格式为JSON，包含问题列表和建议")
                .withComponent(OutputFormatComponent.json())
                .build();
        
        printResult(result);
    }
    
    /**
     * API文档生成示例
     */
    public static void apiDocumentationExample() {
        System.out.println("3. API文档生成示例");
        System.out.println("==================");
        
        PromptResult result = PromptBuilder.create()
                .withSystem("你是一个技术文档专家")
                .withInstruction("为以下API生成详细的技术文档")
                .withVariable("apiName", "getUserInfo")
                .withVariable("description", "根据用户ID获取用户详细信息")
                .withVariable("method", "GET")
                .withVariable("path", "/api/user/{id}")
                .withContext("请求参数", "id: 用户ID（必填）")
                .withContext("响应格式", "JSON格式的用户信息")
                .withExample(
                    "GET /api/user/123",
                    "{ \"id\": 123, \"name\": \"张三\", \"email\": \"<EMAIL>\" }"
                )
                .withComponent(OutputFormatComponent.markdown())
                .withConstraint("包含请求参数、响应格式和使用示例")
                .build();
        
        printResult(result);
    }
    
    /**
     * 模板使用示例
     */
    public static void templateUsageExample() {
        System.out.println("4. 模板使用示例");
        System.out.println("================");
        
        // 使用Velocity模板
        VelocityPromptTemplate template = new VelocityPromptTemplate();
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("language", "Java");
        variables.put("projectType", "Spring Boot应用");
        variables.put("frameworkVersion", "Spring Boot 2.7.0");
        variables.put("code", "public class Example { }");
        
        PromptResult result = PromptBuilder.create()
                .withTemplate("code-review")
                .withVariables(variables)
                .build();
        
        printResult(result);
    }
    
    /**
     * 自定义组件示例
     */
    public static void customComponentExample() {
        System.out.println("5. 自定义组件示例");
        System.out.println("==================");
        
        // 创建自定义组件
        CustomPromptComponent customComponent = new CustomPromptComponent(
                "这是一个自定义组件，用于特殊的业务逻辑处理"
        );
        
        PromptResult result = PromptBuilder.create()
                .withSystem("你是一个AI助手")
                .withComponent(customComponent)
                .withUser("请处理自定义组件中的内容")
                .build();
        
        printResult(result);
    }
    
    /**
     * 处理器链示例
     */
    public static void processorChainExample() {
        System.out.println("6. 处理器链示例");
        System.out.println("================");
        
        PromptResult result = PromptBuilder.create()
                .withSystem("你是一个${role}")
                .withInstruction("请${action}以下内容")
                .withVariable("role", "代码审查专家")
                .withVariable("action", "分析")
                .withProcessor(new ValidationProcessor(false))  // 验证处理器
                .withProcessor(new TemplateProcessor())         // 模板处理器
                .withUser("这是需要分析的内容")
                .build();
        
        printResult(result);
    }
    
    /**
     * 打印结果
     */
    private static void printResult(PromptResult result) {
        System.out.println("构建成功: " + result.isSuccess());
        System.out.println("组件数量: " + result.getComponents().size());
        System.out.println("构建时间: " + result.getBuildTime());
        
        if (result.isSuccess()) {
            System.out.println("生成的提示词:");
            System.out.println("---");
            System.out.println(result.getContent());
            System.out.println("---");
        } else {
            System.out.println("构建失败: " + result.getError().map(Exception::getMessage).orElse("未知错误"));
        }
        
        if (!result.getWarnings().isEmpty()) {
            System.out.println("警告信息: " + result.getWarnings());
        }
        
        System.out.println();
    }
}
