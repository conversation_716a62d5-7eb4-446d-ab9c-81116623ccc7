package com.yxt.order.assistant.prompt.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 提示词框架配置属性
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
@ConfigurationProperties(prefix = "prompt")
public class PromptProperties {
    
    /**
     * 是否启用缓存
     */
    private boolean enableCache = true;
    
    /**
     * 模板路径
     */
    private String templatePath = "templates/";
    
    /**
     * 默认编码
     */
    private String defaultEncoding = "UTF-8";
    
    /**
     * 模板引擎类型
     */
    private TemplateEngine templateEngine = TemplateEngine.VELOCITY;
    
    /**
     * 是否启用严格验证模式
     */
    private boolean strictValidation = false;
    
    /**
     * 默认组件分隔符
     */
    private String componentSeparator = "\n\n";
    
    /**
     * 最大内容长度
     */
    private int maxContentLength = 10000;
    
    /**
     * 缓存配置
     */
    private Cache cache = new Cache();
    
    public boolean isEnableCache() {
        return enableCache;
    }
    
    public void setEnableCache(boolean enableCache) {
        this.enableCache = enableCache;
    }
    
    public String getTemplatePath() {
        return templatePath;
    }
    
    public void setTemplatePath(String templatePath) {
        this.templatePath = templatePath;
    }
    
    public String getDefaultEncoding() {
        return defaultEncoding;
    }
    
    public void setDefaultEncoding(String defaultEncoding) {
        this.defaultEncoding = defaultEncoding;
    }
    
    public TemplateEngine getTemplateEngine() {
        return templateEngine;
    }
    
    public void setTemplateEngine(TemplateEngine templateEngine) {
        this.templateEngine = templateEngine;
    }
    
    public boolean isStrictValidation() {
        return strictValidation;
    }
    
    public void setStrictValidation(boolean strictValidation) {
        this.strictValidation = strictValidation;
    }
    
    public String getComponentSeparator() {
        return componentSeparator;
    }
    
    public void setComponentSeparator(String componentSeparator) {
        this.componentSeparator = componentSeparator;
    }
    
    public int getMaxContentLength() {
        return maxContentLength;
    }
    
    public void setMaxContentLength(int maxContentLength) {
        this.maxContentLength = maxContentLength;
    }
    
    public Cache getCache() {
        return cache;
    }
    
    public void setCache(Cache cache) {
        this.cache = cache;
    }
    
    /**
     * 模板引擎类型枚举
     */
    public enum TemplateEngine {
        VELOCITY,
        SIMPLE
    }
    
    /**
     * 缓存配置
     */
    public static class Cache {
        
        /**
         * 模板缓存大小
         */
        private int templateCacheSize = 100;
        
        /**
         * 模板缓存过期时间（秒）
         */
        private long templateCacheExpireSeconds = 3600;
        
        /**
         * 结果缓存大小
         */
        private int resultCacheSize = 50;
        
        /**
         * 结果缓存过期时间（秒）
         */
        private long resultCacheExpireSeconds = 300;
        
        public int getTemplateCacheSize() {
            return templateCacheSize;
        }
        
        public void setTemplateCacheSize(int templateCacheSize) {
            this.templateCacheSize = templateCacheSize;
        }
        
        public long getTemplateCacheExpireSeconds() {
            return templateCacheExpireSeconds;
        }
        
        public void setTemplateCacheExpireSeconds(long templateCacheExpireSeconds) {
            this.templateCacheExpireSeconds = templateCacheExpireSeconds;
        }
        
        public int getResultCacheSize() {
            return resultCacheSize;
        }
        
        public void setResultCacheSize(int resultCacheSize) {
            this.resultCacheSize = resultCacheSize;
        }
        
        public long getResultCacheExpireSeconds() {
            return resultCacheExpireSeconds;
        }
        
        public void setResultCacheExpireSeconds(long resultCacheExpireSeconds) {
            this.resultCacheExpireSeconds = resultCacheExpireSeconds;
        }
    }
}
