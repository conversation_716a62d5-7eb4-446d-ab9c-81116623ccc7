package com.yxt.order.assistant.prompt.component;

import com.yxt.order.assistant.prompt.core.PromptComponentType;

/**
 * 约束组件
 * 用于定义对输出的限制和要求
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class ConstraintComponent extends AbstractPromptComponent {
    
    public ConstraintComponent(String content) {
        super(PromptComponentType.CONSTRAINT, content);
    }
    
    public ConstraintComponent(String content, int priority) {
        super(PromptComponentType.CONSTRAINT, content, priority);
    }
    
    /**
     * 创建约束组件
     * 
     * @param content 约束内容
     * @return 约束组件实例
     */
    public static ConstraintComponent of(String content) {
        return new ConstraintComponent(content);
    }
    
    /**
     * 创建带优先级的约束组件
     * 
     * @param content 约束内容
     * @param priority 优先级
     * @return 约束组件实例
     */
    public static ConstraintComponent of(String content, int priority) {
        return new ConstraintComponent(content, priority);
    }
}
