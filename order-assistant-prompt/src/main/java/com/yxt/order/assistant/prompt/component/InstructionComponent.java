package com.yxt.order.assistant.prompt.component;

import com.yxt.order.assistant.prompt.core.PromptComponentType;

/**
 * 指令组件
 * 用于提供具体的任务指令
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class InstructionComponent extends AbstractPromptComponent {
    
    public InstructionComponent(String content) {
        super(PromptComponentType.INSTRUCTION, content, true);
    }
    
    public InstructionComponent(String content, int priority) {
        super(PromptComponentType.INSTRUCTION, content, priority, true);
    }
    
    /**
     * 创建指令组件
     * 
     * @param content 指令内容
     * @return 指令组件实例
     */
    public static InstructionComponent of(String content) {
        return new InstructionComponent(content);
    }
    
    /**
     * 创建带优先级的指令组件
     * 
     * @param content 指令内容
     * @param priority 优先级
     * @return 指令组件实例
     */
    public static InstructionComponent of(String content, int priority) {
        return new InstructionComponent(content, priority);
    }
}
