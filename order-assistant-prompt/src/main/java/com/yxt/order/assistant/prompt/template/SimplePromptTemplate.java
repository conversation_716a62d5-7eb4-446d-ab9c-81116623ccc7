package com.yxt.order.assistant.prompt.template;

import com.yxt.order.assistant.prompt.core.PromptTemplate;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 简单的提示词模板实现
 * 支持基本的变量替换，使用 ${variableName} 语法
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class SimplePromptTemplate implements PromptTemplate {
    
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");
    
    @Override
    public String render(String templateContent, Map<String, Object> variables) {
        if (templateContent == null || templateContent.trim().isEmpty()) {
            return "";
        }
        
        if (variables == null || variables.isEmpty()) {
            return templateContent;
        }
        
        String result = templateContent;
        Matcher matcher = VARIABLE_PATTERN.matcher(templateContent);
        
        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object value = variables.get(variableName);
            String replacement = value != null ? value.toString() : "";
            
            // 替换所有匹配的变量
            result = result.replace("${" + variableName + "}", replacement);
        }
        
        return result;
    }
    
    @Override
    public String renderTemplate(String templateName, Map<String, Object> variables) {
        // 简单模板不支持文件模板，直接将模板名称作为内容处理
        return render(templateName, variables);
    }
    
    @Override
    public boolean templateExists(String templateName) {
        // 简单模板不支持文件模板，总是返回true
        return true;
    }
    
    @Override
    public String getEngineName() {
        return "Simple";
    }
    
    @Override
    public TemplateValidationResult validateTemplate(String templateContent) {
        if (templateContent == null) {
            return TemplateValidationResult.failure("Template content cannot be null");
        }
        
        // 检查变量语法是否正确
        Matcher matcher = VARIABLE_PATTERN.matcher(templateContent);
        while (matcher.find()) {
            String variableName = matcher.group(1);
            if (variableName.trim().isEmpty()) {
                return TemplateValidationResult.failure("Empty variable name found: ${" + variableName + "}");
            }
        }
        
        return TemplateValidationResult.success();
    }
    
    /**
     * 提取模板中的所有变量名
     * 
     * @param templateContent 模板内容
     * @return 变量名集合
     */
    public java.util.Set<String> extractVariables(String templateContent) {
        java.util.Set<String> variables = new java.util.HashSet<>();
        
        if (templateContent != null) {
            Matcher matcher = VARIABLE_PATTERN.matcher(templateContent);
            while (matcher.find()) {
                variables.add(matcher.group(1));
            }
        }
        
        return variables;
    }
}
