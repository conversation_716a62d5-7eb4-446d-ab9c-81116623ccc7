package com.yxt.order.assistant.prompt.processor;

import com.yxt.order.assistant.prompt.core.PromptComponent;
import com.yxt.order.assistant.prompt.core.PromptContext;
import com.yxt.order.assistant.prompt.core.PromptProcessor;
import com.yxt.order.assistant.prompt.exception.PromptException;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 验证处理器
 * 用于验证提示词组件的有效性
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class ValidationProcessor implements PromptProcessor {
    
    private final boolean strictMode;
    
    public ValidationProcessor() {
        this(false);
    }
    
    public ValidationProcessor(boolean strictMode) {
        this.strictMode = strictMode;
    }
    
    @Override
    public List<PromptComponent> process(List<PromptComponent> components, PromptContext context) {
        if (components == null || components.isEmpty()) {
            return components;
        }
        
        // 验证所有组件
        List<PromptComponent> invalidComponents = components.stream()
                .filter(component -> !component.validate(context))
                .collect(Collectors.toList());
        
        if (!invalidComponents.isEmpty()) {
            String invalidComponentNames = invalidComponents.stream()
                    .map(component -> component.getType().getDisplayName())
                    .collect(Collectors.joining(", "));
            
            if (strictMode) {
                throw new PromptException("Invalid components found: " + invalidComponentNames);
            } else {
                // 非严格模式下，记录警告并移除无效组件
                System.err.println("Warning: Invalid components removed: " + invalidComponentNames);
                return components.stream()
                        .filter(component -> component.validate(context))
                        .collect(Collectors.toList());
            }
        }
        
        return components;
    }
    
    @Override
    public String getName() {
        return "ValidationProcessor";
    }
    
    @Override
    public int getPriority() {
        return 10; // 高优先级，最先执行
    }
    
    /**
     * 检查是否为严格模式
     * 
     * @return true表示严格模式，false表示宽松模式
     */
    public boolean isStrictMode() {
        return strictMode;
    }
}
