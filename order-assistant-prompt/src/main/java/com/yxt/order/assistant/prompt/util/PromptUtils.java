package com.yxt.order.assistant.prompt.util;

import com.yxt.order.assistant.prompt.core.PromptComponent;
import com.yxt.order.assistant.prompt.core.PromptComponentType;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 提示词工具类
 * 提供常用的提示词处理工具方法
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class PromptUtils {
    
    private PromptUtils() {
        // 工具类，禁止实例化
    }
    
    /**
     * 按类型分组组件
     * 
     * @param components 组件列表
     * @return 按类型分组的组件映射
     */
    public static Map<PromptComponentType, List<PromptComponent>> groupByType(List<PromptComponent> components) {
        if (components == null || components.isEmpty()) {
            return java.util.Collections.emptyMap();
        }
        
        return components.stream()
                .collect(Collectors.groupingBy(PromptComponent::getType));
    }
    
    /**
     * 获取指定类型的组件
     * 
     * @param components 组件列表
     * @param type 组件类型
     * @return 指定类型的组件列表
     */
    public static List<PromptComponent> getComponentsByType(List<PromptComponent> components, 
                                                           PromptComponentType type) {
        if (components == null || components.isEmpty() || type == null) {
            return java.util.Collections.emptyList();
        }
        
        return components.stream()
                .filter(component -> component.getType() == type)
                .collect(Collectors.toList());
    }
    
    /**
     * 检查是否包含指定类型的组件
     * 
     * @param components 组件列表
     * @param type 组件类型
     * @return true表示包含，false表示不包含
     */
    public static boolean hasComponentOfType(List<PromptComponent> components, PromptComponentType type) {
        if (components == null || components.isEmpty() || type == null) {
            return false;
        }
        
        return components.stream()
                .anyMatch(component -> component.getType() == type);
    }
    
    /**
     * 获取第一个指定类型的组件
     * 
     * @param components 组件列表
     * @param type 组件类型
     * @return 第一个指定类型的组件，如果不存在则返回null
     */
    public static PromptComponent getFirstComponentOfType(List<PromptComponent> components, 
                                                         PromptComponentType type) {
        if (components == null || components.isEmpty() || type == null) {
            return null;
        }
        
        return components.stream()
                .filter(component -> component.getType() == type)
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 清理文本内容
     * 移除多余的空白字符和换行符
     * 
     * @param text 原始文本
     * @return 清理后的文本
     */
    public static String cleanText(String text) {
        if (text == null) {
            return null;
        }
        
        return text.trim()
                .replaceAll("\\s+", " ")  // 将多个空白字符替换为单个空格
                .replaceAll("\\n\\s*\\n", "\n\n");  // 将多个连续换行替换为双换行
    }
    
    /**
     * 格式化提示词内容
     * 为不同类型的组件添加适当的格式
     * 
     * @param component 组件
     * @param content 内容
     * @return 格式化后的内容
     */
    public static String formatComponentContent(PromptComponent component, String content) {
        if (component == null || content == null || content.trim().isEmpty()) {
            return content;
        }
        
        PromptComponentType type = component.getType();
        switch (type) {
            case SYSTEM:
                return "## 系统角色\n" + content;
            case CONTEXT:
                return "## 上下文信息\n" + content;
            case INSTRUCTION:
                return "## 任务指令\n" + content;
            case EXAMPLE:
                return "## 示例\n" + content;
            case CONSTRAINT:
                return "## 约束条件\n" + content;
            case OUTPUT_FORMAT:
                return "## 输出格式\n" + content;
            case USER:
                return "## 用户输入\n" + content;
            case ASSISTANT:
                return "## 助手回复\n" + content;
            default:
                return content;
        }
    }
    
    /**
     * 验证变量名是否有效
     * 
     * @param variableName 变量名
     * @return true表示有效，false表示无效
     */
    public static boolean isValidVariableName(String variableName) {
        if (variableName == null || variableName.trim().isEmpty()) {
            return false;
        }
        
        // 变量名只能包含字母、数字、下划线，且不能以数字开头
        return variableName.matches("^[a-zA-Z_][a-zA-Z0-9_]*$");
    }
    
    /**
     * 转义特殊字符
     * 
     * @param text 原始文本
     * @return 转义后的文本
     */
    public static String escapeSpecialCharacters(String text) {
        if (text == null) {
            return null;
        }
        
        return text.replace("\\", "\\\\")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t");
    }
    
    /**
     * 计算文本长度（考虑中文字符）
     * 
     * @param text 文本
     * @return 文本长度
     */
    public static int calculateTextLength(String text) {
        if (text == null) {
            return 0;
        }
        
        int length = 0;
        for (char c : text.toCharArray()) {
            // 中文字符计为2个长度
            if (c >= 0x4e00 && c <= 0x9fff) {
                length += 2;
            } else {
                length += 1;
            }
        }
        
        return length;
    }
}
