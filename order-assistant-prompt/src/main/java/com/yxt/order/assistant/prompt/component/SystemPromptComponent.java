package com.yxt.order.assistant.prompt.component;

import com.yxt.order.assistant.prompt.core.PromptComponentType;
import com.yxt.order.assistant.prompt.core.PromptContext;

/**
 * 系统提示词组件
 * 用于定义AI的角色和基本行为
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class SystemPromptComponent extends AbstractPromptComponent {
    
    public SystemPromptComponent(String content) {
        super(PromptComponentType.SYSTEM, content, true);
    }
    
    public SystemPromptComponent(String content, int priority) {
        super(PromptComponentType.SYSTEM, content, priority, true);
    }
    
    @Override
    protected String processContent(String rawContent, PromptContext context) {
        // 系统提示词通常不需要特殊处理，直接返回原内容
        return rawContent;
    }
    
    /**
     * 创建系统提示词组件
     * 
     * @param content 系统提示词内容
     * @return 系统提示词组件实例
     */
    public static SystemPromptComponent of(String content) {
        return new SystemPromptComponent(content);
    }
    
    /**
     * 创建带优先级的系统提示词组件
     * 
     * @param content 系统提示词内容
     * @param priority 优先级
     * @return 系统提示词组件实例
     */
    public static SystemPromptComponent of(String content, int priority) {
        return new SystemPromptComponent(content, priority);
    }
}
