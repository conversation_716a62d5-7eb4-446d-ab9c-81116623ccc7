package com.yxt.order.assistant.prompt.core;

import java.util.Map;

/**
 * 提示词组件基础接口
 * 所有提示词组件都需要实现此接口
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public interface PromptComponent {
    
    /**
     * 获取组件类型
     * 
     * @return 组件类型
     */
    PromptComponentType getType();
    
    /**
     * 获取组件内容
     * 
     * @param context 上下文信息
     * @return 组件内容
     */
    String getContent(PromptContext context);
    
    /**
     * 获取组件优先级，用于排序
     * 数值越小优先级越高
     * 
     * @return 优先级
     */
    default int getPriority() {
        return getType().getDefaultPriority();
    }
    
    /**
     * 验证组件是否有效
     * 
     * @param context 上下文信息
     * @return 验证结果
     */
    default boolean validate(PromptContext context) {
        return true;
    }
    
    /**
     * 获取组件的变量依赖
     * 
     * @return 变量依赖映射
     */
    default Map<String, Class<?>> getVariableDependencies() {
        return java.util.Collections.emptyMap();
    }
    
    /**
     * 是否为必需组件
     * 
     * @return true表示必需，false表示可选
     */
    default boolean isRequired() {
        return false;
    }
}
