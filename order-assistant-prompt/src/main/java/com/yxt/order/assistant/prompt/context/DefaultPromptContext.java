package com.yxt.order.assistant.prompt.context;

import com.yxt.order.assistant.prompt.core.PromptContext;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 默认提示词上下文实现
 * 线程安全的上下文管理器
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class DefaultPromptContext implements PromptContext {
    
    private final Map<String, Object> variables;
    private final Map<String, Object> attributes;
    private final PromptContext parent;
    
    public DefaultPromptContext() {
        this(null);
    }
    
    public DefaultPromptContext(PromptContext parent) {
        this.variables = new ConcurrentHashMap<>();
        this.attributes = new ConcurrentHashMap<>();
        this.parent = parent;
    }
    
    @Override
    public PromptContext setVariable(String key, Object value) {
        if (key == null) {
            throw new IllegalArgumentException("Variable key cannot be null");
        }
        if (value != null) {
            variables.put(key, value);
        } else {
            variables.remove(key);
        }
        return this;
    }
    
    @Override
    public PromptContext setVariables(Map<String, Object> variables) {
        if (variables != null) {
            variables.forEach(this::setVariable);
        }
        return this;
    }
    
    @Override
    public Optional<Object> getVariable(String key) {
        if (key == null) {
            return Optional.empty();
        }
        
        Object value = variables.get(key);
        if (value != null) {
            return Optional.of(value);
        }
        
        // 如果当前上下文中没有，尝试从父上下文获取
        if (parent != null) {
            return parent.getVariable(key);
        }
        
        return Optional.empty();
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public <T> T getVariable(String key, T defaultValue) {
        Optional<Object> value = getVariable(key);
        if (value.isPresent()) {
            try {
                return (T) value.get();
            } catch (ClassCastException e) {
                // 类型转换失败，返回默认值
                return defaultValue;
            }
        }
        return defaultValue;
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public <T> Optional<T> getVariable(String key, Class<T> type) {
        Optional<Object> value = getVariable(key);
        if (value.isPresent() && type.isInstance(value.get())) {
            return Optional.of((T) value.get());
        }
        return Optional.empty();
    }
    
    @Override
    public Map<String, Object> getAllVariables() {
        Map<String, Object> allVariables = new ConcurrentHashMap<>();
        
        // 先添加父上下文的变量
        if (parent != null) {
            allVariables.putAll(parent.getAllVariables());
        }
        
        // 再添加当前上下文的变量（会覆盖父上下文中的同名变量）
        allVariables.putAll(variables);
        
        return java.util.Collections.unmodifiableMap(allVariables);
    }
    
    @Override
    public boolean hasVariable(String key) {
        return getVariable(key).isPresent();
    }
    
    @Override
    public PromptContext removeVariable(String key) {
        if (key != null) {
            variables.remove(key);
        }
        return this;
    }
    
    @Override
    public PromptContext clearVariables() {
        variables.clear();
        return this;
    }
    
    @Override
    public PromptContext setAttribute(String key, Object value) {
        if (key == null) {
            throw new IllegalArgumentException("Attribute key cannot be null");
        }
        if (value != null) {
            attributes.put(key, value);
        } else {
            attributes.remove(key);
        }
        return this;
    }
    
    @Override
    public Optional<Object> getAttribute(String key) {
        if (key == null) {
            return Optional.empty();
        }
        
        Object value = attributes.get(key);
        if (value != null) {
            return Optional.of(value);
        }
        
        // 如果当前上下文中没有，尝试从父上下文获取
        if (parent != null) {
            return parent.getAttribute(key);
        }
        
        return Optional.empty();
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key, T defaultValue) {
        Optional<Object> value = getAttribute(key);
        if (value.isPresent()) {
            try {
                return (T) value.get();
            } catch (ClassCastException e) {
                // 类型转换失败，返回默认值
                return defaultValue;
            }
        }
        return defaultValue;
    }
    
    @Override
    public PromptContext createChild() {
        return new DefaultPromptContext(this);
    }
    
    @Override
    public Optional<PromptContext> getParent() {
        return Optional.ofNullable(parent);
    }
    
    @Override
    public String toString() {
        return "DefaultPromptContext{" +
                "variables=" + variables.size() +
                ", attributes=" + attributes.size() +
                ", hasParent=" + (parent != null) +
                '}';
    }
}
