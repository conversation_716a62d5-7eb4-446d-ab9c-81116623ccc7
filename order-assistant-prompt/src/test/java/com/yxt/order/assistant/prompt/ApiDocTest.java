package com.yxt.order.assistant.prompt;

import com.yxt.order.assistant.prompt.core.PromptBuilder;
import com.yxt.order.assistant.prompt.core.PromptResult;
import com.yxt.order.assistant.prompt.processor.TemplateProcessor;

/**
 * API文档生成场景专项测试
 * 演示如何在提示词中使用API相关变量
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class ApiDocTest {
    
    public static void main(String[] args) {
        System.out.println("=== API文档生成场景 - 变量使用演示 ===");
        testApiVariableUsage();
    }
    
    public static void testApiVariableUsage() {
        System.out.println("设置的API变量:");
        System.out.println("- apiName: getUserInfo");
        System.out.println("- description: 获取用户信息");
        System.out.println("- method: GET");
        System.out.println("- path: /api/user/{id}");
        System.out.println();
        
        // 构建API文档生成提示词
        PromptResult result = PromptBuilder.create()
                .withSystem("你是一个技术文档专家")
                .withInstruction("为以下API生成详细的技术文档：\n\n" +
                        "- **API名称**: ${apiName}\n" +
                        "- **描述**: ${description}\n" +
                        "- **请求方法**: ${method}\n" +
                        "- **请求路径**: ${path}")
                .withVariable("apiName", "getUserInfo")      // API名称变量
                .withVariable("description", "获取用户信息")   // 描述变量
                .withVariable("method", "GET")               // HTTP方法变量
                .withVariable("path", "/api/user/{id}")      // 路径变量
                .withOutputFormat("Markdown格式")
                .withConstraint("包含请求参数、响应格式和示例")
                .withProcessor(new TemplateProcessor())      // 关键：添加模板处理器
                .build();
        
        System.out.println("构建结果: " + (result.isSuccess() ? "✓ 成功" : "✗ 失败"));
        System.out.println("组件数量: " + result.getComponents().size());
        System.out.println();
        
        System.out.println("生成的完整提示词:");
        System.out.println("==================");
        System.out.println(result.getContent());
        System.out.println("==================");
        System.out.println();
        
        // 验证变量是否被正确替换
        String content = result.getContent();
        boolean apiNameReplaced = content.contains("getUserInfo") && !content.contains("${apiName}");
        boolean descriptionReplaced = content.contains("获取用户信息") && !content.contains("${description}");
        boolean methodReplaced = content.contains("GET") && !content.contains("${method}");
        boolean pathReplaced = content.contains("/api/user/{id}") && !content.contains("${path}");
        
        System.out.println("变量替换验证:");
        System.out.println("- apiName变量替换: " + (apiNameReplaced ? "✓ 成功" : "✗ 失败"));
        System.out.println("- description变量替换: " + (descriptionReplaced ? "✓ 成功" : "✗ 失败"));
        System.out.println("- method变量替换: " + (methodReplaced ? "✓ 成功" : "✗ 失败"));
        System.out.println("- path变量替换: " + (pathReplaced ? "✓ 成功" : "✗ 失败"));
        
        boolean allSuccess = apiNameReplaced && descriptionReplaced && methodReplaced && pathReplaced;
        System.out.println();
        System.out.println("总体结果: " + (allSuccess ? "✓ 所有变量替换成功" : "✗ 部分变量替换失败"));
        
        System.out.println();
        System.out.println("总结:");
        System.out.println("在API文档生成场景中，变量的作用是:");
        System.out.println("1. withVariable() 设置API的各种属性（名称、描述、方法、路径）");
        System.out.println("2. 在指令模板中使用 ${变量名} 引用这些属性");
        System.out.println("3. TemplateProcessor 将变量替换为实际值");
        System.out.println("4. 生成包含具体API信息的结构化提示词");
        System.out.println("5. 这样可以用同一个模板为不同的API生成文档");
    }
}
